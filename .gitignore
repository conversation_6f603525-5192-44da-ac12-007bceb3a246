# Dependencies
node_modules/

# Expo
.expo/
.expo-shared/
dist/
web-build/
expo-env.d.ts

# Native builds (created during prebuild/eject)
ios/
android/

# Sensitive credentials
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.pem

# Metro bundler
.metro-health-check*

# Debug logs
yarn-debug.*
yarn-error.*
*.log

# macOS
.DS_Store

# Environment configs
.env*.local
.env
.env.development
.env.staging
.env.production

# TypeScript
*.tsbuildinfo

# Jest/coverage
coverage/

# IDE/Editor configs (optional)
.vscode/
.idea/

# Lockfile management
package-lock.json   # using yarn, ignore npm's lockfile

