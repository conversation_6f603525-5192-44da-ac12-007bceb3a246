# Environment Variables Setup

This project uses environment variables to manage configuration across different environments (development, staging, production).

## Quick Setup

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Fill in your Supabase credentials in `.env`:**
   ```
   EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   ```

## Environment Files

- **`.env`** - Default environment file (used for development)
- **`.env.development`** - Development-specific variables
- **`.env.production`** - Production-specific variables
- **`.env.example`** - Template file with example values

## Getting Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to Settings → API
4. Copy the following:
   - **Project URL** → `EXPO_PUBLIC_SUPABASE_URL`
   - **Project API keys → anon public** → `EXPO_PUBLIC_SUPABASE_ANON_KEY`

## Important Notes

- **Environment variables must start with `EXPO_PUBLIC_`** to be accessible in client-side code
- **Never commit `.env` files** to version control (they're in `.gitignore`)
- **Always use `.env.example`** to document required variables for other developers
- **The app will throw an error** if required environment variables are missing

## Usage in Code

Environment variables are accessed using `process.env`:

```typescript
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
```

## Environment-Specific Builds

For different environments, you can use different `.env` files:

```bash
# Development (uses .env or .env.development)
npx expo start

# Production build
# Copy .env.production to .env before building
cp .env.production .env
npx expo build
```

## Security Best Practices

1. **Never hardcode sensitive values** in your source code
2. **Use different Supabase projects** for development and production
3. **Regularly rotate API keys** in production
4. **Review environment variables** before each deployment
5. **Use EAS Secrets** for production builds when using EAS Build

## EAS Build Integration

For EAS builds, you can set environment variables in `eas.json` or use EAS Secrets:

```bash
# Set secrets for EAS Build
eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_URL --value "your-url"
eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_ANON_KEY --value "your-key"
```

## Troubleshooting

- **"Missing Supabase environment variables" error**: Check that your `.env` file exists and contains the required variables
- **Variables not loading**: Restart your development server after changing `.env` files
- **Build issues**: Ensure environment variables are properly set for your build environment
